#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运维流程GUI工具
用于处理运营和技术文件夹的签名前处理
"""

import os
import sys
import json
import zipfile
import rarfile
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import threading
import logging
from datetime import datetime

class MaintenanceGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("运维流程处理工具")
        self.root.geometry("800x600")
        
        # 配置文件路径
        self.config_file = "maintenance_config.json"
        self.config = self.load_config()
        
        # 选中的文件夹
        self.selected_folders = []
        self.operation_folder = None  # 运营文件夹
        self.technical_folder = None  # 技术文件夹
        
        # 设置日志
        self.setup_logging()
        
        # 创建界面
        self.create_widgets()
        
        # 加载默认路径
        if self.config.get("data_folder"):
            self.data_folder_var.set(self.config["data_folder"])
            self.refresh_folder_tree()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.log_message(f"加载配置文件失败: {e}")
        return {}
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存配置文件失败: {e}")
    
    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger('maintenance')
        self.logger.setLevel(logging.INFO)
        
        # 创建日志处理器
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 数据文件夹选择
        ttk.Label(main_frame, text="数据文件夹:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        folder_frame = ttk.Frame(main_frame)
        folder_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        folder_frame.columnconfigure(0, weight=1)
        
        self.data_folder_var = tk.StringVar(value=self.config.get("data_folder", ""))
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.data_folder_var)
        self.folder_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).grid(row=0, column=1)
        
        # 文件夹树形视图
        ttk.Label(main_frame, text="文件夹选择:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=5)
        
        tree_frame = ttk.Frame(main_frame)
        tree_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # 创建树形视图
        self.tree = ttk.Treeview(tree_frame, selectmode='extended')
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 滚动条
        tree_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        tree_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=tree_scroll.set)
        
        # 绑定事件
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Control-Button-1>', self.on_ctrl_click)
        
        # 按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, pady=10)
        
        ttk.Button(button_frame, text="刷新", command=self.refresh_folder_tree).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="签名前处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)
        
        # 日志输出区域
        ttk.Label(main_frame, text="日志输出:").grid(row=3, column=0, sticky=(tk.W, tk.N), pady=5)
        
        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=3, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=60)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        main_frame.rowconfigure(3, weight=1)
    
    def log_message(self, message):
        """输出日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # 在GUI中显示
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        
        # 同时输出到控制台
        print(log_entry.strip())
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(
            title="选择数据文件夹",
            initialdir=self.data_folder_var.get()
        )
        if folder:
            self.data_folder_var.set(folder)
            self.config["data_folder"] = folder
            self.save_config()
            self.refresh_folder_tree()
    
    def refresh_folder_tree(self):
        """刷新文件夹树"""
        # 清空树
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        data_folder = self.data_folder_var.get()
        if not data_folder or not os.path.exists(data_folder):
            self.log_message("数据文件夹不存在")
            return
        
        try:
            self.populate_tree("", data_folder)
            self.log_message("文件夹树已刷新")
        except Exception as e:
            self.log_message(f"刷新文件夹树失败: {e}")
    
    def populate_tree(self, parent, path):
        """填充树形视图"""
        try:
            for item in sorted(os.listdir(path)):
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    node = self.tree.insert(parent, 'end', text=item, values=[item_path])
                    # 检查是否有子文件夹
                    try:
                        if any(os.path.isdir(os.path.join(item_path, sub)) for sub in os.listdir(item_path)):
                            self.tree.insert(node, 'end', text='...')  # 占位符
                    except PermissionError:
                        pass
        except PermissionError:
            self.log_message(f"无权限访问: {path}")
    
    def on_double_click(self, event):
        """双击事件 - 进入文件夹"""
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return
        
        # 获取文件夹路径
        folder_path = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
        if not folder_path or not os.path.isdir(folder_path):
            return
        
        # 清空当前项的子项
        for child in self.tree.get_children(item):
            self.tree.delete(child)
        
        # 重新填充子项
        self.populate_tree(item, folder_path)
        
        # 展开节点
        self.tree.item(item, open=True)
    
    def on_ctrl_click(self, event):
        """Ctrl+点击事件 - 多选文件夹"""
        item = self.tree.selection()[-1] if self.tree.selection() else None
        if not item:
            return
        
        folder_path = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
        if not folder_path or not os.path.isdir(folder_path):
            return
        
        folder_name = os.path.basename(folder_path)
        
        # 检查是否是运营或技术文件夹
        if "运营" in folder_name:
            self.operation_folder = folder_path
            self.log_message(f"已选择运营文件夹: {folder_path}")
        elif "技术" in folder_name:
            self.technical_folder = folder_path
            self.log_message(f"已选择技术文件夹: {folder_path}")
        else:
            self.log_message(f"警告: 文件夹 '{folder_name}' 不包含'运营'或'技术'关键字")
    
    def start_processing(self):
        """开始签名前处理"""
        if not self.operation_folder and not self.technical_folder:
            messagebox.showwarning("警告", "请先选择运营或技术文件夹")
            return
        
        # 在新线程中执行处理
        thread = threading.Thread(target=self.process_folders)
        thread.daemon = True
        thread.start()
    
    def process_folders(self):
        """处理文件夹"""
        try:
            self.log_message("开始签名前处理...")
            
            if self.operation_folder:
                self.log_message(f"处理运营文件夹: {self.operation_folder}")
                self.process_operation_folder(self.operation_folder)
            
            if self.technical_folder:
                self.log_message(f"处理技术文件夹: {self.technical_folder}")
                self.process_technical_folder(self.technical_folder)
            
            self.log_message("签名前处理完成!")
            
        except Exception as e:
            self.log_message(f"处理过程中发生错误: {e}")
    
    def extract_archive(self, archive_path, extract_to):
        """解压文件"""
        try:
            if archive_path.lower().endswith('.zip'):
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
                self.log_message(f"已解压ZIP文件: {archive_path}")
            elif archive_path.lower().endswith('.rar'):
                with rarfile.RarFile(archive_path, 'r') as rar_ref:
                    rar_ref.extractall(extract_to)
                self.log_message(f"已解压RAR文件: {archive_path}")
            else:
                self.log_message(f"不支持的压缩格式: {archive_path}")
                return False
            return True
        except Exception as e:
            self.log_message(f"解压失败 {archive_path}: {e}")
            return False
    
    def find_data_folder(self, base_path):
        """查找data文件夹"""
        for root, dirs, files in os.walk(base_path):
            if 'data' in [d.lower() for d in dirs]:
                data_path = os.path.join(root, 'data')
                self.log_message(f"找到data文件夹: {data_path}")
                return data_path
        return None
    
    def find_game_exe(self, base_path):
        """查找game.exe文件"""
        for root, dirs, files in os.walk(base_path):
            for file in files:
                if file.lower() == 'game.exe':
                    game_path = os.path.join(root, file)
                    self.log_message(f"找到game.exe: {game_path}")
                    return game_path
        return None
    
    def process_operation_folder(self, folder_path):
        """处理运营文件夹"""
        # 查找并解压压缩文件
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if file.lower().endswith(('.zip', '.rar')):
                extract_path = os.path.join(folder_path, f"extracted_{file}")
                if self.extract_archive(file_path, extract_path):
                    # 查找data文件夹
                    data_folder = self.find_data_folder(extract_path)
                    if data_folder:
                        self.log_message(f"运营文件夹处理完成，data文件夹位于: {data_folder}")
                        # 这里可以调用SFile.exe进行打包
                        # TODO: 集成SFile.exe调用
                    else:
                        self.log_message("未找到data文件夹")
    
    def process_technical_folder(self, folder_path):
        """处理技术文件夹"""
        # 查找并解压压缩文件
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if file.lower().endswith(('.zip', '.rar')):
                extract_path = os.path.join(folder_path, f"extracted_{file}")
                if self.extract_archive(file_path, extract_path):
                    # 查找game.exe
                    game_exe = self.find_game_exe(extract_path)
                    if game_exe:
                        self.log_message(f"技术文件夹处理完成，game.exe位于: {game_exe}")
                        # 这里可以进行后续的加壳处理
                        # TODO: 集成Themida.exe调用
                    else:
                        self.log_message("未找到game.exe文件")

def main():
    root = tk.Tk()
    app = MaintenanceGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
