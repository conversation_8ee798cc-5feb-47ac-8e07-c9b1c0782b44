#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
运维流程GUI工具
用于处理运营和技术文件夹的签名前处理
"""

import os
import sys
import json
import zipfile
import subprocess
import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
from pathlib import Path
import threading
import logging
from datetime import datetime

# 尝试导入rarfile，如果失败则使用命令行工具
try:
    import rarfile
    RARFILE_AVAILABLE = True
except ImportError:
    RARFILE_AVAILABLE = False

class MaintenanceGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("运维流程处理工具")
        self.root.geometry("800x600")
        
        # 配置文件路径
        self.config_file = "maintenance_config.json"
        self.config = self.load_config()
        
        # 选中的文件夹
        self.selected_folders = []
        self.operation_folder = None  # 运营文件夹
        self.technical_folder = None  # 技术文件夹
        self.selected_items = set()  # 记录已选中的树节点
        
        # 设置日志
        self.setup_logging()
        
        # 创建界面
        self.create_widgets()
        
        # 加载默认路径
        if self.config.get("data_folder"):
            self.data_folder_var.set(self.config["data_folder"])
            self.refresh_folder_tree()
    
    def load_config(self):
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.log_message(f"加载配置文件失败: {e}")
        return {}
    
    def save_config(self):
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"保存配置文件失败: {e}")
    
    def setup_logging(self):
        """设置日志"""
        self.logger = logging.getLogger('maintenance')
        self.logger.setLevel(logging.INFO)
        
        # 创建日志处理器
        handler = logging.StreamHandler()
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)
    
    def create_widgets(self):
        """创建界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(2, weight=1)  # 文件夹树列可以扩展
        main_frame.rowconfigure(1, weight=1)     # 文件夹选择行可以扩展
        
        # 数据文件夹选择
        ttk.Label(main_frame, text="数据文件夹:").grid(row=0, column=0, sticky=tk.W, pady=5)
        
        folder_frame = ttk.Frame(main_frame)
        folder_frame.grid(row=0, column=1, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        folder_frame.columnconfigure(0, weight=1)
        
        self.data_folder_var = tk.StringVar(value=self.config.get("data_folder", ""))
        self.folder_entry = ttk.Entry(folder_frame, textvariable=self.data_folder_var)
        self.folder_entry.grid(row=0, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        ttk.Button(folder_frame, text="浏览", command=self.browse_folder).grid(row=0, column=1)
        
        # 文件夹选择区域
        ttk.Label(main_frame, text="文件夹选择:").grid(row=1, column=0, sticky=(tk.W, tk.N), pady=5)

        # 左侧按钮框架（竖直排列）
        left_button_frame = ttk.Frame(main_frame)
        left_button_frame.grid(row=1, column=1, sticky=(tk.N, tk.W), pady=5, padx=(0, 5))

        ttk.Button(left_button_frame, text="选择文件夹", command=self.select_current_folder).pack(pady=2)
        ttk.Button(left_button_frame, text="清除选择", command=self.clear_selection).pack(pady=2)

        # 文件夹树形视图
        tree_frame = ttk.Frame(main_frame)
        tree_frame.grid(row=1, column=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # 创建树形视图
        self.tree = ttk.Treeview(tree_frame, selectmode='extended')
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置树形视图的标签和颜色
        self.tree.tag_configure('selected_operation', background='lightblue')
        self.tree.tag_configure('selected_technical', background='lightgreen')

        # 滚动条
        tree_scroll = ttk.Scrollbar(tree_frame, orient="vertical", command=self.tree.yview)
        tree_scroll.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=tree_scroll.set)

        # 绑定事件
        self.tree.bind('<Double-1>', self.on_double_click)
        self.tree.bind('<Button-1>', self.on_single_click)
        
        # 底部按钮框架
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=10)

        ttk.Button(button_frame, text="刷新", command=self.refresh_folder_tree).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="签名前处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)

        # 右侧清除日志按钮
        log_button_frame = ttk.Frame(main_frame)
        log_button_frame.grid(row=3, column=2, sticky=tk.E, pady=(0, 5))
        ttk.Button(log_button_frame, text="清除日志", command=self.clear_log).pack()
        
        # 日志输出区域
        ttk.Label(main_frame, text="日志输出:").grid(row=4, column=0, sticky=(tk.W, tk.N), pady=5)

        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=4, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=5)
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)

        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, width=60)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        main_frame.rowconfigure(4, weight=1)
    
    def log_message(self, message):
        """输出日志消息"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # 在GUI中显示
        if hasattr(self, 'log_text'):
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        
        # 同时输出到控制台
        print(log_entry.strip())
    
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(
            title="选择数据文件夹",
            initialdir=self.data_folder_var.get()
        )
        if folder:
            self.data_folder_var.set(folder)
            self.config["data_folder"] = folder
            self.save_config()
            self.refresh_folder_tree()
    
    def refresh_folder_tree(self):
        """刷新文件夹树"""
        # 清空树和选择
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.clear_selection()

        data_folder = self.data_folder_var.get()
        if not data_folder or not os.path.exists(data_folder):
            self.log_message("数据文件夹不存在")
            return

        try:
            self.populate_tree("", data_folder, auto_expand=False)
            self.log_message("文件夹树已刷新")
        except Exception as e:
            self.log_message(f"刷新文件夹树失败: {e}")

    def clear_selection(self):
        """清除所有选择"""
        self.selected_items.clear()
        self.operation_folder = None
        self.technical_folder = None

        # 清除所有标签
        for item in self.tree.get_children():
            self.clear_item_tags(item)

        self.log_message("已清除所有选择")

    def clear_item_tags(self, item):
        """递归清除项目标签"""
        self.tree.item(item, tags=())
        for child in self.tree.get_children(item):
            self.clear_item_tags(child)
    
    def populate_tree(self, parent, path, auto_expand=False):
        """填充树形视图"""
        try:
            for item in sorted(os.listdir(path)):
                item_path = os.path.join(path, item)
                if os.path.isdir(item_path):
                    node = self.tree.insert(parent, 'end', text=item, values=[item_path])

                    # 检查是否有子文件夹，如果有则添加占位符
                    try:
                        subdirs = [sub for sub in os.listdir(item_path)
                                 if os.path.isdir(os.path.join(item_path, sub))]
                        if subdirs:
                            # 添加占位符，表示有子目录
                            self.tree.insert(node, 'end', text='...')
                    except PermissionError:
                        pass

        except PermissionError:
            self.log_message(f"无权限访问: {path}")
    
    def on_single_click(self, event):
        """单击事件 - 选择项目"""
        pass

    def on_double_click(self, event):
        """双击事件 - 展开/折叠文件夹"""
        # 获取双击的项目
        item = self.tree.identify_row(event.y)
        if not item:
            return

        # 获取文件夹路径
        folder_path = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
        if not folder_path or not os.path.isdir(folder_path):
            return

        # 检查是否已经展开
        if self.tree.item(item, 'open'):
            # 如果已展开，则折叠
            self.tree.item(item, open=False)
            self.log_message(f"已折叠文件夹: {os.path.basename(folder_path)}")
        else:
            # 如果未展开，则展开
            # 检查是否有占位符
            children = self.tree.get_children(item)
            if children and self.tree.item(children[0], 'text') == '...':
                # 清空占位符
                for child in children:
                    self.tree.delete(child)
                # 重新填充子项
                self.populate_tree(item, folder_path)
                self.log_message(f"已展开文件夹: {os.path.basename(folder_path)}")

            # 展开节点
            self.tree.item(item, open=True)

    def select_current_folder(self):
        """选择当前选中的文件夹"""
        selected_items = self.tree.selection()
        if not selected_items:
            self.log_message("请先在文件夹树中选择一个文件夹")
            return

        item = selected_items[0]  # 取第一个选中的项目
        folder_path = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
        if not folder_path or not os.path.isdir(folder_path):
            self.log_message("选中的不是有效文件夹")
            return

        folder_name = os.path.basename(folder_path)

        # 检查是否已经选中
        if item in self.selected_items:
            # 取消选择
            self.selected_items.remove(item)
            self.tree.item(item, tags=())

            if "运营" in folder_name and self.operation_folder == folder_path:
                self.operation_folder = None
                self.log_message(f"已取消选择运营文件夹: {folder_name}")
            elif "技术" in folder_name and self.technical_folder == folder_path:
                self.technical_folder = None
                self.log_message(f"已取消选择技术文件夹: {folder_name}")
        else:
            # 添加选择
            # 检查是否是运营或技术文件夹
            if "运营" in folder_name:
                # 如果已有运营文件夹，先清除之前的选择
                if self.operation_folder:
                    self.clear_folder_selection("运营")

                self.selected_items.add(item)
                self.operation_folder = folder_path
                self.tree.item(item, tags=('selected_operation',))
                self.log_message(f"已选择运营文件夹: {folder_name}")

            elif "技术" in folder_name:
                # 如果已有技术文件夹，先清除之前的选择
                if self.technical_folder:
                    self.clear_folder_selection("技术")

                self.selected_items.add(item)
                self.technical_folder = folder_path
                self.tree.item(item, tags=('selected_technical',))
                self.log_message(f"已选择技术文件夹: {folder_name}")

            else:
                self.log_message(f"警告: 文件夹 '{folder_name}' 不包含'运营'或'技术'关键字")
                return

        # 显示当前选择状态
        self.show_selection_status()

    def clear_folder_selection(self, folder_type):
        """清除指定类型的文件夹选择"""
        items_to_remove = []
        for item in self.selected_items:
            folder_path = self.tree.item(item, 'values')[0] if self.tree.item(item, 'values') else None
            if folder_path:
                folder_name = os.path.basename(folder_path)
                if folder_type in folder_name:
                    items_to_remove.append(item)
                    self.tree.item(item, tags=())

        for item in items_to_remove:
            self.selected_items.remove(item)

    def clear_log(self):
        """清除日志"""
        if hasattr(self, 'log_text'):
            self.log_text.delete(1.0, tk.END)
            self.log_message("日志已清除")

    def show_selection_status(self):
        """显示当前选择状态"""
        status_parts = []
        if self.operation_folder:
            status_parts.append(f"运营: {os.path.basename(self.operation_folder)}")
        if self.technical_folder:
            status_parts.append(f"技术: {os.path.basename(self.technical_folder)}")

        if status_parts:
            status = "当前选择: " + " | ".join(status_parts)
        else:
            status = "当前选择: 无"

        self.log_message(status)

    def start_processing(self):
        """开始签名前处理"""
        if not self.operation_folder and not self.technical_folder:
            messagebox.showwarning("警告", "请先选择运营或技术文件夹")
            return
        
        # 在新线程中执行处理
        thread = threading.Thread(target=self.process_folders)
        thread.daemon = True
        thread.start()
    
    def process_folders(self):
        """处理文件夹"""
        try:
            self.log_message("开始签名前处理...")
            
            if self.operation_folder:
                self.log_message(f"处理运营文件夹: {self.operation_folder}")
                self.process_operation_folder(self.operation_folder)
            
            if self.technical_folder:
                self.log_message(f"处理技术文件夹: {self.technical_folder}")
                self.process_technical_folder(self.technical_folder)
            
            self.log_message("签名前处理完成!")
            
        except Exception as e:
            self.log_message(f"处理过程中发生错误: {e}")
    
    def extract_archive(self, archive_path, extract_to):
        """解压文件到指定目录"""
        try:
            # 确保解压目录存在
            os.makedirs(extract_to, exist_ok=True)

            if archive_path.lower().endswith('.zip'):
                with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                    zip_ref.extractall(extract_to)
                self.log_message(f"已解压ZIP文件到: {extract_to}")
                return True

            elif archive_path.lower().endswith('.rar'):
                # 尝试多种RAR解压方法
                return self.extract_rar_file(archive_path, extract_to)
            else:
                self.log_message(f"不支持的压缩格式: {archive_path}")
                return False

        except Exception as e:
            self.log_message(f"解压失败 {archive_path}: {e}")
            return False

    def extract_rar_file(self, rar_path, extract_to):
        """解压RAR文件，尝试多种方法"""
        # 方法1: 使用rarfile库
        if RARFILE_AVAILABLE:
            try:
                # 尝试设置RAR工具路径
                possible_rar_paths = [
                    r"C:\Program Files\WinRAR\UnRAR.exe",
                    r"C:\Program Files (x86)\WinRAR\UnRAR.exe",
                    r"C:\Program Files\WinRAR\Rar.exe",
                    r"C:\Program Files (x86)\WinRAR\Rar.exe",
                    "unrar",  # 如果在PATH中
                    "rar"     # 如果在PATH中
                ]

                for rar_tool in possible_rar_paths:
                    if os.path.exists(rar_tool) or rar_tool in ["unrar", "rar"]:
                        try:
                            rarfile.UNRAR_TOOL = rar_tool
                            with rarfile.RarFile(rar_path, 'r') as rar_ref:
                                rar_ref.extractall(extract_to)
                            self.log_message(f"已解压RAR文件到: {extract_to} (使用 {rar_tool})")
                            return True
                        except Exception as e:
                            self.log_message(f"使用 {rar_tool} 解压失败: {e}")
                            continue
            except Exception as e:
                self.log_message(f"rarfile库解压失败: {e}")

        # 方法2: 使用命令行工具
        return self.extract_rar_with_command(rar_path, extract_to)

    def extract_rar_with_command(self, rar_path, extract_to):
        """使用命令行工具解压RAR"""
        commands_to_try = [
            # WinRAR命令行
            [r"C:\Program Files\WinRAR\UnRAR.exe", "x", "-y", rar_path, extract_to + "\\"],
            [r"C:\Program Files (x86)\WinRAR\UnRAR.exe", "x", "-y", rar_path, extract_to + "\\"],
            [r"C:\Program Files\WinRAR\Rar.exe", "x", "-y", rar_path, extract_to + "\\"],
            [r"C:\Program Files (x86)\WinRAR\Rar.exe", "x", "-y", rar_path, extract_to + "\\"],
            # 7-Zip命令行
            [r"C:\Program Files\7-Zip\7z.exe", "x", rar_path, f"-o{extract_to}", "-y"],
            [r"C:\Program Files (x86)\7-Zip\7z.exe", "x", rar_path, f"-o{extract_to}", "-y"],
            # 系统PATH中的工具
            ["unrar", "x", "-y", rar_path, extract_to + "\\"],
            ["rar", "x", "-y", rar_path, extract_to + "\\"],
            ["7z", "x", rar_path, f"-o{extract_to}", "-y"]
        ]

        for cmd in commands_to_try:
            try:
                # 检查工具是否存在
                if not os.path.exists(cmd[0]) and cmd[0] not in ["unrar", "rar", "7z"]:
                    continue

                self.log_message(f"尝试使用命令: {' '.join(cmd)}")
                result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

                if result.returncode == 0:
                    self.log_message(f"已解压RAR文件到: {extract_to} (使用 {cmd[0]})")
                    return True
                else:
                    self.log_message(f"命令执行失败 ({cmd[0]}): {result.stderr}")

            except subprocess.TimeoutExpired:
                self.log_message(f"解压超时: {cmd[0]}")
            except FileNotFoundError:
                self.log_message(f"工具未找到: {cmd[0]}")
            except Exception as e:
                self.log_message(f"命令执行异常 ({cmd[0]}): {e}")

        # 如果所有方法都失败，提供手动解压建议
        self.log_message("自动解压RAR失败，请考虑以下解决方案：")
        self.log_message("1. 安装WinRAR或7-Zip软件")
        self.log_message("2. 手动解压RAR文件到指定目录")
        self.log_message("3. 将RAR文件转换为ZIP格式")
        return False
    
    def find_data_folder(self, base_path):
        """查找data文件夹"""
        for root, dirs, files in os.walk(base_path):
            if 'data' in [d.lower() for d in dirs]:
                data_path = os.path.join(root, 'data')
                self.log_message(f"找到data文件夹: {data_path}")
                return data_path
        return None
    
    def find_game_exe(self, base_path):
        """查找game.exe文件"""
        for root, dirs, files in os.walk(base_path):
            for file in files:
                if file.lower() == 'game.exe':
                    game_path = os.path.join(root, file)
                    self.log_message(f"找到game.exe: {game_path}")
                    return game_path
        return None
    
    def get_archive_name_without_extension(self, archive_path):
        """获取压缩文件名（不含扩展名）"""
        filename = os.path.basename(archive_path)
        # 移除.zip, .rar等扩展名
        for ext in ['.zip', '.rar', '.7z']:
            if filename.lower().endswith(ext):
                return filename[:-len(ext)]
        return filename

    def is_already_extracted(self, folder_path, archive_path):
        """检查是否已经解压过"""
        archive_name = self.get_archive_name_without_extension(archive_path)

        # 检查是否存在同名文件夹
        for item in os.listdir(folder_path):
            item_path = os.path.join(folder_path, item)
            if os.path.isdir(item_path) and item == archive_name:
                self.log_message(f"检测到已解压的文件夹: {item}")
                return True, item_path

        return False, None

    def process_operation_folder(self, folder_path):
        """处理运营文件夹"""
        # 查找并解压压缩文件
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if file.lower().endswith(('.zip', '.rar')):
                self.log_message(f"发现压缩文件: {file}")

                # 检查是否已经解压过
                already_extracted, extracted_path = self.is_already_extracted(folder_path, file_path)

                if already_extracted:
                    self.log_message(f"文件已解压，跳过解压步骤")
                    search_path = extracted_path
                else:
                    # 直接解压到当前文件夹
                    if self.extract_archive(file_path, folder_path):
                        # 获取解压后的文件夹路径
                        archive_name = self.get_archive_name_without_extension(file_path)
                        search_path = os.path.join(folder_path, archive_name)
                        if not os.path.exists(search_path):
                            # 如果没有创建同名文件夹，直接在当前文件夹搜索
                            search_path = folder_path
                    else:
                        continue

                # 查找data文件夹
                data_folder = self.find_data_folder(search_path)
                if data_folder:
                    self.log_message(f"运营文件夹处理完成，data文件夹位于: {data_folder}")
                    # 这里可以调用SFile.exe进行打包
                    # TODO: 集成SFile.exe调用
                else:
                    self.log_message("未找到data文件夹")

    def process_technical_folder(self, folder_path):
        """处理技术文件夹"""
        # 查找并解压压缩文件
        for file in os.listdir(folder_path):
            file_path = os.path.join(folder_path, file)
            if file.lower().endswith(('.zip', '.rar')):
                self.log_message(f"发现压缩文件: {file}")

                # 检查是否已经解压过
                already_extracted, extracted_path = self.is_already_extracted(folder_path, file_path)

                if already_extracted:
                    self.log_message(f"文件已解压，跳过解压步骤")
                    search_path = extracted_path
                else:
                    # 直接解压到当前文件夹
                    if self.extract_archive(file_path, folder_path):
                        # 获取解压后的文件夹路径
                        archive_name = self.get_archive_name_without_extension(file_path)
                        search_path = os.path.join(folder_path, archive_name)
                        if not os.path.exists(search_path):
                            # 如果没有创建同名文件夹，直接在当前文件夹搜索
                            search_path = folder_path
                    else:
                        continue

                # 查找game.exe
                game_exe = self.find_game_exe(search_path)
                if game_exe:
                    self.log_message(f"技术文件夹处理完成，game.exe位于: {game_exe}")
                    # 这里可以进行后续的加壳处理
                    # TODO: 集成Themida.exe调用
                else:
                    self.log_message("未找到game.exe文件")

def main():
    root = tk.Tk()
    app = MaintenanceGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
