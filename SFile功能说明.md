# SFile打包功能说明

## 功能概述

本工具集成了SFile打包功能，用于处理运营文件夹中的data目录，生成游戏更新所需的文件。

## 工作流程

### 1. 自动检测和处理
- 程序会自动在运营文件夹中查找data目录
- 找到data目录后，自动启动SFile打包流程

### 2. SFile打包过程
1. **创建临时目录**: 在data目录的父目录创建`~patch`文件夹
2. **文件扫描**: 递归扫描data目录下的所有文件
3. **生成SAF文件**: 将所有文件内容打包到`update.saf`
4. **生成SAH文件**: 创建包含文件索引信息的`update.sah`
5. **创建最终目录**: 创建`patch~`文件夹并移动生成的文件

### 3. 输出文件
- `update.saf`: 包含所有文件数据的二进制文件
- `update.sah`: 包含文件索引和目录结构的头文件

## 文件格式说明

### SAF文件格式
- 连续存储所有文件的二进制数据
- 每个文件后添加一个NULL字节作为分隔符
- 文件按照扫描顺序存储

### SAH文件格式
```
[4字节] 版本号
[4字节] 文件总数
[目录结构数据]
[4字节] 空列表数量(0)
```

#### 目录结构格式
```
[4字节] 目录名长度
[变长] 目录名(UTF-8 + NULL)
[4字节] 文件数量
[文件信息列表]
[4字节] 子目录数量
[子目录列表]
```

#### 文件信息格式
```
[4字节] 文件名长度
[变长] 文件名(UTF-8 + NULL)
[4字节] 文件在SAF中的偏移量
[4字节] 文件大小
[4字节] 文件修改时间(Unix时间戳)
```

## 使用方法

1. **选择运营文件夹**: 确保文件夹名包含"运营"关键字
2. **自动解压**: 程序会自动解压文件夹中的压缩文件
3. **查找data目录**: 程序会在解压后的文件中查找data文件夹
4. **自动打包**: 找到data文件夹后自动开始SFile打包
5. **查看结果**: 在日志中查看打包进度和结果

## 输出目录结构

```
运营文件夹/
├── 解压的文件/
│   ├── Client/
│   │   └── data/          # 源data目录
│   └── ...
└── patch~/                # 最终输出目录
    ├── update.saf         # 数据文件
    └── update.sah         # 头文件
```

## 错误处理

### 常见错误及解决方案

1. **未找到data文件夹**
   - 检查压缩文件是否正确解压
   - 确认文件夹结构是否正确

2. **文件访问权限错误**
   - 确保程序有读写权限
   - 检查文件是否被其他程序占用

3. **磁盘空间不足**
   - 确保有足够的磁盘空间存储打包文件
   - SAF文件大小约等于所有源文件大小之和

## 技术特点

1. **纯Python实现**: 不依赖原始SFile.exe，提高兼容性
2. **内存优化**: 逐文件处理，避免大文件导致内存不足
3. **错误恢复**: 详细的错误日志和异常处理
4. **进度显示**: 实时显示处理进度和文件信息

## 兼容性

- 生成的文件格式与原始SFile.exe完全兼容
- 支持中文文件名和路径
- 支持大文件和深层目录结构
- 兼容Windows文件系统特性

## 性能优化

- 使用二进制模式读写文件，提高效率
- 批量处理文件信息，减少I/O操作
- 优化内存使用，支持大型data目录

## 注意事项

1. 确保data目录中没有正在使用的文件
2. 打包过程中不要修改源文件
3. 生成的patch~文件夹可以直接用于游戏更新
4. 建议在打包前备份重要文件
