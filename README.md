# 运维流程处理工具

这是一个基于Python Tkinter的GUI工具，用于自动化处理运营和技术文件夹的签名前处理流程。

## 功能特性

1. **数据文件夹管理**
   - 支持选择和保存默认数据文件夹路径
   - 自动保存配置到本地文件

2. **文件夹浏览**
   - 树形视图显示文件夹结构
   - 双击进入子文件夹
   - Ctrl+点击多选文件夹

3. **自动处理**
   - 自动识别运营文件夹（包含"运营"关键字）
   - 自动识别技术文件夹（包含"技术"关键字）
   - 自动解压ZIP和RAR文件
   - 查找运营文件夹中的data目录
   - 查找技术文件夹中的game.exe文件

4. **日志输出**
   - 实时显示处理进度和结果
   - 带时间戳的详细日志

## 安装依赖

```bash
pip install -r requirements.txt
```

注意：如果系统没有安装WinRAR，需要下载并安装UnRAR工具来支持RAR文件解压。

## 使用方法

1. 运行程序：
   ```bash
   python maintenance_gui.py
   ```

2. 选择数据文件夹（程序会记住这个路径）

3. 在文件夹树中浏览并找到目标文件夹

4. 使用Ctrl+点击选择包含"运营"和"技术"关键字的文件夹

5. 点击"签名前处理"开始自动处理

## 处理流程

### 运营文件夹处理
1. 自动解压文件夹中的ZIP/RAR文件
2. 在解压后的文件中查找data文件夹
3. 为后续SFile.exe打包做准备

### 技术文件夹处理
1. 自动解压文件夹中的ZIP/RAR文件
2. 在解压后的文件中查找game.exe
3. 为后续Themida加壳处理做准备

## 配置文件

程序会在当前目录生成`maintenance_config.json`配置文件，用于保存：
- 数据文件夹路径
- 其他用户设置

## 扩展功能

当前版本实现了基础的文件夹处理和文件查找功能。后续可以集成：
- SFile.exe命令行调用
- Themida.exe自动加壳
- MD5计算和launcher.shaiya文件更新
- 更多自动化处理步骤

## 注意事项

1. 确保选择的文件夹名称包含"运营"或"技术"关键字
2. 压缩文件应该包含正确的目录结构
3. 运营文件夹解压后应该能找到唯一的data文件夹
4. 技术文件夹解压后应该能找到game.exe文件
